#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试coordinator属性修复效果的脚本
"""

import sys
import os
import logging
import yaml
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from kafka_data_filter.auto_cleanup_manager import AutoCleanupManager

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

def test_coordinator_fix():
    """测试coordinator属性修复效果"""
    print("🧪 测试coordinator属性修复效果")
    print("=" * 60)
    
    # 读取配置
    config_path = project_root / "example_config.yaml"
    if not config_path.exists():
        print(f"❌ 配置文件不存在: {config_path}")
        return False
    
    try:
        # 加载配置文件
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)

        # 提取kafka配置
        kafka_config = config.get('kafka', {})

        # 初始化自动清理管理器
        cleanup_manager = AutoCleanupManager(kafka_config)
        
        # 测试消费者组ID（使用配置中的group_id）
        group_id = kafka_config.get('group_id', 'kafka-filter-demo')
        
        print(f"📋 测试消费者组: {group_id}")
        print("-" * 40)
        
        # 测试1: 检查消费者组是否存在
        print("1️⃣ 测试消费者组存在性检查...")
        exists, check_info = cleanup_manager.group_manager.consumer_group_exists(
            group_id,
            retry_count=2,
            retry_delay=1.0
        )
        
        print(f"   结果: exists={exists}, info={check_info}")
        
        if exists:
            # 测试2: 获取详细信息（这里应该不再出现coordinator错误）
            print("2️⃣ 测试获取消费者组详细信息...")
            group_info = cleanup_manager.group_manager.get_consumer_group_info(
                group_id,
                retry_count=2,
                retry_delay=1.0
            )
            
            print(f"   详细信息获取结果:")
            print(f"     存在: {group_info.get('exists', False)}")
            print(f"     状态: {group_info.get('state', 'Unknown')}")
            print(f"     成员数: {group_info.get('member_count', 0)}")
            
            # 重点检查coordinator信息
            coordinator = group_info.get('coordinator', {})
            print(f"     Coordinator信息:")
            print(f"       ID: {coordinator.get('id', 'N/A')}")
            print(f"       Host: {coordinator.get('host', 'N/A')}")
            print(f"       Port: {coordinator.get('port', 'N/A')}")
            
            if 'note' in coordinator:
                print(f"       说明: {coordinator['note']}")
            if 'error' in coordinator:
                print(f"       错误: {coordinator['error']}")
            
            # 检查是否还有错误
            if group_info.get('exists', False):
                print("   ✅ 详细信息获取成功，coordinator属性问题已修复")
            else:
                error_msg = group_info.get('error', '')
                if 'coordinator' in error_msg:
                    print("   ❌ coordinator属性问题仍然存在")
                    print(f"   错误信息: {error_msg}")
                else:
                    print(f"   ⚠️ 其他错误: {error_msg}")
        else:
            print("   ℹ️ 消费者组不存在，跳过详细信息测试")
        
        # 测试3: 测试自动清理流程（不实际执行删除）
        print("3️⃣ 测试自动清理流程（模拟）...")
        try:
            # 这里只是测试清理逻辑，不实际删除
            cleanup_manager._cleanup_consumer_group("测试退出")
            print("   ✅ 自动清理流程测试完成，无coordinator相关错误")
        except Exception as e:
            if 'coordinator' in str(e):
                print(f"   ❌ 自动清理中仍有coordinator错误: {e}")
            else:
                print(f"   ⚠️ 其他错误: {e}")
        
        cleanup_manager.close()
        
        print("\n" + "=" * 60)
        print("🎉 测试完成")
        print("📝 如果没有看到coordinator相关的AttributeError，说明修复成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    setup_logging()
    
    print("🔧 Kafka数据过滤器 - Coordinator属性修复测试")
    print("=" * 60)
    
    success = test_coordinator_fix()
    
    if success:
        print("\n✅ 测试执行完成")
    else:
        print("\n❌ 测试执行失败")
        sys.exit(1)
