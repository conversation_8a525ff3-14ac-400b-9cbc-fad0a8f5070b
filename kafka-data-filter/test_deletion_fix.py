#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试消费者组删除操作修复效果的脚本
"""

import sys
import logging
from pathlib import Path
from unittest.mock import Mock, MagicMock
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from kafka_data_filter.kafka_consumer_group_manager import KafkaConsumerGroupManager

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

def create_mock_manager():
    """创建模拟的KafkaConsumerGroupManager"""
    manager = KafkaConsumerGroupManager.__new__(KafkaConsumerGroupManager)
    manager.logger = logging.getLogger(__name__)
    manager.admin_client = Mock()
    return manager

def test_deletion_result_scenarios():
    """测试不同的删除结果场景"""
    print("🧪 测试消费者组删除结果验证修复")
    print("=" * 60)
    
    manager = create_mock_manager()
    group_id = "test-group"
    
    # 场景1: 标准kafka-python库格式 - 成功
    print("1️⃣ 测试标准kafka-python库格式（成功）...")
    mock_future_success = Mock()
    mock_future_success.result.return_value = None  # 成功删除
    delete_result_1 = {group_id: mock_future_success}
    
    result = manager._check_deletion_result(group_id, delete_result_1)
    print(f"   结果: {result}")
    assert result is True, "应该返回True表示删除成功"
    print("   ✅ 标准格式成功场景测试通过")
    
    # 场景2: 标准kafka-python库格式 - 失败
    print("\n2️⃣ 测试标准kafka-python库格式（失败）...")
    mock_future_fail = Mock()
    mock_future_fail.result.side_effect = Exception("删除失败")
    delete_result_2 = {group_id: mock_future_fail}
    
    result = manager._check_deletion_result(group_id, delete_result_2)
    print(f"   结果: {result}")
    assert result is False, "应该返回False表示删除失败"
    print("   ✅ 标准格式失败场景测试通过")
    
    # 场景3: kafka-python-ng格式 - 空字典（原问题场景）
    print("\n3️⃣ 测试kafka-python-ng格式（空字典）...")
    delete_result_3 = {}
    
    result = manager._check_deletion_result(group_id, delete_result_3)
    print(f"   结果: {result}")
    assert result is None, "应该返回None表示需要进一步验证"
    print("   ✅ 空字典场景测试通过")
    
    # 场景4: kafka-python-ng格式 - 不包含目标group_id的字典
    print("\n4️⃣ 测试kafka-python-ng格式（不包含目标group_id）...")
    mock_other_future = Mock()
    mock_other_future.result.return_value = None
    delete_result_4 = {"other-group": mock_other_future}
    
    result = manager._check_deletion_result(group_id, delete_result_4)
    print(f"   结果: {result}")
    # 这种情况下，应该尝试通过其他键获取结果
    print("   ✅ 不包含目标group_id场景测试通过")
    
    # 场景5: 其他格式 - 直接是Future对象
    print("\n5️⃣ 测试其他格式（直接是Future对象）...")
    mock_direct_future = Mock()
    mock_direct_future.result.return_value = None
    
    result = manager._check_deletion_result(group_id, mock_direct_future)
    print(f"   结果: {result}")
    assert result is True, "应该返回True表示删除成功"
    print("   ✅ 直接Future对象场景测试通过")
    
    print("\n" + "=" * 60)
    print("🎉 删除结果验证逻辑测试完成")
    
    return True

def test_complete_deletion_flow():
    """测试完整的删除流程"""
    print("\n🧪 测试完整删除流程")
    print("=" * 60)
    
    manager = create_mock_manager()
    group_id = "test-group"
    
    # 模拟get_consumer_group_info返回存在的消费者组
    manager.get_consumer_group_info = Mock(return_value={'exists': True})
    
    # 模拟backup_consumer_group_offsets
    manager.backup_consumer_group_offsets = Mock(return_value="backup.json")
    
    # 模拟check_group_safety_for_deletion
    manager.check_group_safety_for_deletion = Mock(return_value=(True, []))
    
    # 模拟consumer_group_exists - 删除后返回不存在
    manager.consumer_group_exists = Mock(return_value=(False, "not_found"))
    
    # 场景1: kafka-python-ng返回空字典（原问题场景）
    print("1️⃣ 测试kafka-python-ng空字典场景...")
    manager.admin_client.delete_consumer_groups.return_value = {}
    
    result = manager.delete_consumer_group(group_id, force=False, backup=True)
    print(f"   删除结果: {result}")
    
    # 验证是否调用了consumer_group_exists进行验证
    manager.consumer_group_exists.assert_called()
    
    assert result is True, "应该通过验证确认删除成功"
    print("   ✅ 空字典场景通过验证确认删除成功")
    
    # 场景2: 标准成功场景
    print("\n2️⃣ 测试标准成功场景...")
    mock_future = Mock()
    mock_future.result.return_value = None
    manager.admin_client.delete_consumer_groups.return_value = {group_id: mock_future}
    
    # 重置mock
    manager.consumer_group_exists.reset_mock()
    
    result = manager.delete_consumer_group(group_id, force=False, backup=True)
    print(f"   删除结果: {result}")
    
    # 这种情况下不应该调用consumer_group_exists验证
    manager.consumer_group_exists.assert_not_called()
    
    assert result is True, "应该直接确认删除成功"
    print("   ✅ 标准成功场景测试通过")
    
    print("\n" + "=" * 60)
    print("🎉 完整删除流程测试完成")
    
    return True

if __name__ == "__main__":
    setup_logging()
    
    print("🔧 Kafka数据过滤器 - 删除操作修复测试")
    print("=" * 60)
    
    try:
        success1 = test_deletion_result_scenarios()
        success2 = test_complete_deletion_flow()
        
        if success1 and success2:
            print("\n✅ 所有测试通过")
            print("📝 修复后的删除逻辑能够正确处理各种返回值格式")
            print("📝 特别是kafka-python-ng的空字典返回值问题已解决")
        else:
            print("\n❌ 部分测试失败")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")
        sys.exit(1)
