#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟测试coordinator属性修复效果的脚本
不需要真实的Kafka连接
"""

import sys
import logging
from pathlib import Path
from unittest.mock import Mock, MagicMock

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from kafka_data_filter.kafka_consumer_group_manager import KafkaConsumerGroupManager

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

class MockGroupInfoWithoutCoordinator:
    """模拟没有coordinator属性的GroupInformation对象（kafka-python-ng）"""
    def __init__(self):
        self.state = "Stable"
        self.protocol_type = "consumer"
        self.protocol = "range"
        self.members = []
        # 故意不设置coordinator属性

    def __getattr__(self, name):
        if name == 'coordinator':
            raise AttributeError("'GroupInformation' object has no attribute 'coordinator'")
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

def create_mock_group_info_without_coordinator():
    """创建没有coordinator属性的模拟GroupInformation对象（模拟kafka-python-ng）"""
    return MockGroupInfoWithoutCoordinator()

def create_mock_group_info_with_coordinator():
    """创建有coordinator属性的模拟GroupInformation对象（模拟标准kafka-python）"""
    mock_group_info = Mock()
    mock_group_info.state = "Stable"
    mock_group_info.protocol_type = "consumer"
    mock_group_info.protocol = "range"
    mock_group_info.members = []
    
    # 创建coordinator对象
    mock_coordinator = Mock()
    mock_coordinator.nodeId = 1
    mock_coordinator.host = "kafka-broker-1"
    mock_coordinator.port = 9092
    mock_group_info.coordinator = mock_coordinator
    
    return mock_group_info

def test_coordinator_fix_mock():
    """模拟测试coordinator属性修复效果"""
    print("🧪 模拟测试coordinator属性修复效果")
    print("=" * 60)
    
    # 创建模拟的KafkaConsumerGroupManager
    kafka_config = {
        'bootstrap_servers': 'localhost:9092',
        'group_id': 'test-group'
    }
    
    # 创建管理器实例（不初始化真实连接）
    manager = KafkaConsumerGroupManager.__new__(KafkaConsumerGroupManager)
    manager.logger = logging.getLogger(__name__)
    
    print("1️⃣ 测试没有coordinator属性的情况（kafka-python-ng）...")
    
    # 测试场景1：没有coordinator属性
    mock_group_info_ng = create_mock_group_info_without_coordinator()
    
    try:
        coordinator_info = manager._get_coordinator_info_safe(mock_group_info_ng)
        print(f"   ✅ 成功处理没有coordinator属性的情况")
        print(f"   返回的coordinator信息: {coordinator_info}")
        
        # 验证返回的默认值
        assert coordinator_info['id'] == -1
        assert coordinator_info['host'] == 'unknown'
        assert coordinator_info['port'] == -1
        assert 'note' in coordinator_info
        print(f"   ✅ 默认值验证通过")
        
    except AttributeError as e:
        if 'coordinator' in str(e):
            print(f"   ❌ 仍然存在coordinator属性错误: {e}")
            return False
        else:
            print(f"   ⚠️ 其他AttributeError: {e}")
    except Exception as e:
        print(f"   ⚠️ 其他错误: {e}")
    
    print("\n2️⃣ 测试有coordinator属性的情况（标准kafka-python）...")
    
    # 测试场景2：有coordinator属性
    mock_group_info_std = create_mock_group_info_with_coordinator()
    
    try:
        coordinator_info = manager._get_coordinator_info_safe(mock_group_info_std)
        print(f"   ✅ 成功处理有coordinator属性的情况")
        print(f"   返回的coordinator信息: {coordinator_info}")
        
        # 验证返回的真实值
        assert coordinator_info['id'] == 1
        assert coordinator_info['host'] == 'kafka-broker-1'
        assert coordinator_info['port'] == 9092
        print(f"   ✅ 真实值验证通过")
        
    except Exception as e:
        print(f"   ❌ 处理有coordinator属性时出错: {e}")
        return False
    
    print("\n3️⃣ 测试完整的get_consumer_group_info流程...")
    
    # 模拟完整的获取消费者组信息流程
    try:
        # 创建模拟的admin_client
        mock_admin_client = Mock()
        manager.admin_client = mock_admin_client
        
        # 模拟describe_consumer_groups返回
        mock_admin_client.describe_consumer_groups.return_value = {
            'test-group': mock_group_info_ng
        }
        
        # 模拟其他必要的方法
        manager._parse_describe_groups_result = Mock(return_value=({'test-group': mock_group_info_ng}, mock_group_info_ng))
        manager._get_group_offsets = Mock(return_value={})
        manager._get_group_members = Mock(return_value=[])
        
        # 这里应该不会抛出coordinator相关的AttributeError
        result = manager.get_consumer_group_info('test-group', retry_count=1)
        
        print(f"   ✅ 完整流程测试成功")
        print(f"   结果存在性: {result.get('exists', False)}")
        
        if result.get('exists', False):
            coordinator = result.get('coordinator', {})
            print(f"   Coordinator信息: ID={coordinator.get('id')}, Host={coordinator.get('host')}, Port={coordinator.get('port')}")
            if 'note' in coordinator:
                print(f"   说明: {coordinator['note']}")
        
    except AttributeError as e:
        if 'coordinator' in str(e):
            print(f"   ❌ 完整流程中仍有coordinator错误: {e}")
            return False
        else:
            print(f"   ⚠️ 其他AttributeError: {e}")
    except Exception as e:
        print(f"   ⚠️ 其他错误: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 模拟测试完成")
    print("📝 所有测试场景都没有出现coordinator相关的AttributeError")
    print("✅ 修复效果验证成功！")
    
    return True

if __name__ == "__main__":
    setup_logging()
    
    print("🔧 Kafka数据过滤器 - Coordinator属性修复模拟测试")
    print("=" * 60)
    
    success = test_coordinator_fix_mock()
    
    if success:
        print("\n✅ 模拟测试执行成功")
    else:
        print("\n❌ 模拟测试执行失败")
        sys.exit(1)
