#!/usr/bin/env python3
"""
专门针对kafka-python-ng库的测试脚本

测试和验证kafka-python-ng库的特定API行为和返回格式
"""

import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from kafka_data_filter.config_manager import ConfigManager
from kafka_data_filter.kafka_consumer_group_manager import KafkaConsumerGroupManager


def setup_logging():
    """配置日志系统"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('test_kafka_python_ng.log', encoding='utf-8')
        ]
    )
    return logging.getLogger(__name__)


def detect_kafka_library():
    """检测当前使用的Kafka库"""
    print("🔍 检测Kafka库版本")
    print("=" * 50)
    
    try:
        import kafka
        print(f"✅ 成功导入kafka库")
        print(f"库路径: {kafka.__file__}")
        
        if hasattr(kafka, '__version__'):
            print(f"版本: {kafka.__version__}")
        
        # 检查是否是kafka-python-ng
        if 'kafka-python-ng' in str(kafka.__file__) or 'ng' in getattr(kafka, '__version__', ''):
            print("✅ 检测到 kafka-python-ng 库")
            return 'kafka-python-ng'
        else:
            print("✅ 检测到标准 kafka-python 库")
            return 'kafka-python'
            
    except ImportError as e:
        print(f"❌ 无法导入kafka库: {e}")
        return None


def test_kafka_python_ng_list_groups():
    """测试kafka-python-ng的list_consumer_groups方法"""
    logger = setup_logging()
    
    print("\n🔍 测试 kafka-python-ng list_consumer_groups")
    print("=" * 60)
    
    try:
        # 加载配置
        config_manager = ConfigManager("filter_config.yaml")
        kafka_config = config_manager.get_kafka_config()
        
        print(f"Kafka服务器: {kafka_config['bootstrap_servers']}")
        
        # 创建消费者组管理器
        group_manager = KafkaConsumerGroupManager(
            bootstrap_servers=kafka_config['bootstrap_servers']
        )
        
        print("\n1. 直接调用 list_consumer_groups():")
        print("-" * 40)
        
        all_groups = group_manager.admin_client.list_consumer_groups()
        
        print(f"返回类型: {type(all_groups)}")
        print(f"返回内容: {all_groups}")
        
        # 检查是否有特殊属性
        special_attrs = ['result', 'value', '__iter__', '__len__']
        for attr in special_attrs:
            if hasattr(all_groups, attr):
                print(f"✅ 有属性 {attr}")
                if attr == 'result':
                    try:
                        result = all_groups.result()
                        print(f"  result() 返回: {type(result)} - {result}")
                    except Exception as e:
                        print(f"  result() 调用失败: {e}")
                elif attr == 'value':
                    try:
                        value = all_groups.value
                        print(f"  value 属性: {type(value)} - {value}")
                    except Exception as e:
                        print(f"  value 访问失败: {e}")
            else:
                print(f"❌ 无属性 {attr}")
        
        print("\n2. 测试提取group_ids:")
        print("-" * 40)
        
        group_ids = group_manager._extract_group_ids(all_groups)
        print(f"提取的消费者组ID: {group_ids}")
        
        if group_ids:
            print("✅ 成功提取消费者组ID")
        else:
            print("❌ 未能提取到消费者组ID")
        
        group_manager.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_kafka_python_ng_describe_groups():
    """测试kafka-python-ng的describe_consumer_groups方法"""
    logger = setup_logging()
    
    print("\n🔍 测试 kafka-python-ng describe_consumer_groups")
    print("=" * 60)
    
    try:
        # 加载配置
        config_manager = ConfigManager("filter_config.yaml")
        kafka_config = config_manager.get_kafka_config()
        group_id = kafka_config.get('group_id', 'moye-check-data')
        
        print(f"目标消费者组: {group_id}")
        
        # 创建消费者组管理器
        group_manager = KafkaConsumerGroupManager(
            bootstrap_servers=kafka_config['bootstrap_servers']
        )
        
        print("\n1. 直接调用 describe_consumer_groups():")
        print("-" * 40)
        
        group_metadata_raw = group_manager.admin_client.describe_consumer_groups([group_id])
        
        print(f"返回类型: {type(group_metadata_raw)}")
        print(f"返回内容: {group_metadata_raw}")
        
        # 检查特殊属性
        special_attrs = ['result', 'value', '__iter__', '__len__', 'keys']
        for attr in special_attrs:
            if hasattr(group_metadata_raw, attr):
                print(f"✅ 有属性 {attr}")
                if attr == 'result':
                    try:
                        result = group_metadata_raw.result()
                        print(f"  result() 返回: {type(result)} - {result}")
                    except Exception as e:
                        print(f"  result() 调用失败: {e}")
                elif attr == 'value':
                    try:
                        value = group_metadata_raw.value
                        print(f"  value 属性: {type(value)} - {value}")
                    except Exception as e:
                        print(f"  value 访问失败: {e}")
                elif attr == 'keys':
                    try:
                        keys = group_metadata_raw.keys()
                        print(f"  keys() 返回: {list(keys)}")
                    except Exception as e:
                        print(f"  keys() 调用失败: {e}")
            else:
                print(f"❌ 无属性 {attr}")
        
        print("\n2. 测试解析方法:")
        print("-" * 40)
        
        group_metadata, group_info = group_manager._parse_describe_groups_result(
            group_metadata_raw, group_id
        )
        
        if group_metadata is not None and group_info is not None:
            print("✅ 解析成功")
            print(f"解析后的group_metadata类型: {type(group_metadata)}")
            print(f"解析后的group_info类型: {type(group_info)}")
            print(f"group_info内容: {group_info}")
            
            if hasattr(group_info, '__dict__'):
                print(f"group_info属性: {group_info.__dict__}")
        else:
            print("❌ 解析失败")
        
        print("\n3. 测试完整的get_consumer_group_info:")
        print("-" * 40)
        
        detailed_info = group_manager.get_consumer_group_info(group_id)
        print(f"详细信息获取结果:")
        print(f"  存在: {detailed_info.get('exists', False)}")
        print(f"  状态: {detailed_info.get('state', 'Unknown')}")
        print(f"  成员数: {detailed_info.get('member_count', 0)}")
        print(f"  原因: {detailed_info.get('reason', 'N/A')}")
        print(f"  错误: {detailed_info.get('error', 'N/A')}")
        
        group_manager.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_complete_cleanup_flow():
    """测试完整的自动清理流程"""
    logger = setup_logging()
    
    print("\n🧹 测试完整的自动清理流程")
    print("=" * 60)
    
    try:
        # 加载配置
        config_manager = ConfigManager("filter_config.yaml")
        kafka_config = config_manager.get_kafka_config()
        
        # 导入自动清理管理器
        from kafka_data_filter.auto_cleanup_manager import AutoCleanupManager
        
        # 创建自动清理管理器（禁用自动清理，手动测试）
        cleanup_manager = AutoCleanupManager(
            kafka_config=kafka_config,
            cleanup_on_exit=False,  # 禁用自动清理
            backup_before_cleanup=False
        )
        
        if not cleanup_manager.group_manager:
            print("❌ 消费者组管理器未初始化")
            return False
        
        group_id = kafka_config.get('group_id', 'moye-check-data')
        print(f"目标消费者组: {group_id}")
        
        print("\n模拟完整的自动清理检测流程:")
        print("-" * 50)
        
        # 等待状态稳定
        print("1. 等待Kafka集群状态稳定...")
        import time
        time.sleep(2)
        
        # 轻量级检查
        print("2. 执行轻量级检查...")
        exists, check_info = cleanup_manager.group_manager.consumer_group_exists(
            group_id, 
            retry_count=3, 
            retry_delay=1.0
        )
        
        print(f"   结果: exists={exists}, info={check_info}")
        
        # 如果轻量级检查失败，尝试备用方法
        if not exists and 'error' in check_info:
            print("3. 轻量级检查失败，尝试备用检查方法...")
            exists, check_info = cleanup_manager.group_manager.consumer_group_exists_fallback(group_id)
            print(f"   备用检查结果: exists={exists}, info={check_info}")
        
        if exists:
            # 详细信息确认
            print("4. 获取消费者组详细信息进行二次确认...")
            group_info = cleanup_manager.group_manager.get_consumer_group_info(
                group_id, 
                retry_count=2, 
                retry_delay=1.0
            )
            
            print(f"   详细信息获取结果:")
            print(f"     存在: {group_info.get('exists', False)}")
            print(f"     状态: {group_info.get('state', 'Unknown')}")
            print(f"     成员数: {group_info.get('member_count', 0)}")
            print(f"     原因: {group_info.get('reason', 'N/A')}")
            print(f"     错误: {group_info.get('error', 'N/A')}")
            
            if group_info.get('exists', False):
                print("✅ 消费者组存在，自动清理检测成功")
                print("📝 在实际运行中，这里会继续执行删除操作")
                
                # 测试安全检查
                print("5. 测试安全检查...")
                is_safe, warnings = cleanup_manager.group_manager.check_group_safety_for_deletion(group_id)
                print(f"   安全检查结果: is_safe={is_safe}")
                if warnings:
                    print("   警告信息:")
                    for warning in warnings:
                        print(f"     - {warning}")
                
            else:
                print("❌ 详细信息获取失败")
                print("📝 这表明kafka-python-ng的兼容性问题仍需修复")
        else:
            print("❌ 消费者组不存在或检查失败")
        
        cleanup_manager.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 完整流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🧪 kafka-python-ng 兼容性测试")
    print("=" * 80)
    
    # 检测库版本
    library_type = detect_kafka_library()
    
    if library_type != 'kafka-python-ng':
        print(f"⚠️  检测到的库类型: {library_type}")
        print("本测试专门针对kafka-python-ng库，但仍会继续测试...")
    
    tests = [
        ("list_consumer_groups测试", test_kafka_python_ng_list_groups),
        ("describe_consumer_groups测试", test_kafka_python_ng_describe_groups),
        ("完整清理流程测试", test_complete_cleanup_flow)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔬 开始测试: {test_name}")
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
                
        except Exception as e:
            print(f"❌ 测试 {test_name} 执行异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果汇总
    print("\n" + "=" * 80)
    print("测试结果汇总:")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<30} {status}")
        if result:
            passed += 1
    
    print("=" * 80)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！kafka-python-ng兼容性修复成功！")
        print("\n📝 下一步:")
        print("1. 现在可以运行 python main.py filter_config.yaml 测试完整的自动清理")
        print("2. 消费者组应该能被正确检测和删除")
        print("3. 查看日志文件 test_kafka_python_ng.log 获取详细信息")
        return True
    else:
        print("⚠️  部分测试失败，请检查错误信息和日志文件")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"测试执行失败: {e}")
        sys.exit(1)
