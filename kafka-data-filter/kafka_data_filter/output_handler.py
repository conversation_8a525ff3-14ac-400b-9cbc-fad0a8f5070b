"""
输出处理器模块

负责处理过滤后的数据输出，支持控制台输出和文件输出
"""

import json
import os
import logging
import tempfile
import shutil
from typing import Dict, Any, List, Optional, TextIO
from datetime import datetime
from enum import Enum
from pathlib import Path


class OutputType(Enum):
    """输出类型枚举"""
    CONSOLE = "console"
    FILE = "file"
    BOTH = "both"


class OutputFormat(Enum):
    """输出格式枚举"""
    JSON = "json"
    PRETTY_JSON = "pretty_json"
    TEXT = "text"
    JSON_ARRAY = "json_array"


class OutputHandler:
    """输出处理器类，负责处理过滤后的数据输出"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化输出处理器

        参数:
            config: 输出配置字典
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.output_type = OutputType(config.get('type', 'console'))
        self.output_format = OutputFormat(config.get('format', 'pretty_json'))
        self.file_path = None
        self.file_handle = None
        self.output_count = 0

        # JSON数组相关属性
        self.json_array_messages = []
        self.json_array_config = config.get('json_array', {})
        self.batch_size = self.json_array_config.get('batch_size', 10)
        self.incremental_append = self.json_array_config.get('incremental_append', True)
        self.new_file_on_startup = self.json_array_config.get('new_file_on_startup', True)

        # 验证配置
        self._validate_config()

        # 生成文件路径
        self._generate_file_path()

        # 初始化文件输出
        if self.output_type in [OutputType.FILE, OutputType.BOTH]:
            self._init_file_output()
    
    def _validate_config(self) -> None:
        """验证输出配置"""
        if self.output_type in [OutputType.FILE, OutputType.BOTH]:
            # 检查是否有输出目录或文件路径配置
            output_directory = self.config.get('output_directory')
            file_path = self.config.get('file_path')

            if not output_directory and not file_path:
                raise ValueError("文件输出需要指定output_directory或file_path")

    def _generate_file_path(self) -> None:
        """生成文件路径"""
        try:
            output_directory = self.config.get('output_directory')
            file_naming = self.config.get('file_naming', {})
            use_timestamp = file_naming.get('use_timestamp', False)

            if output_directory:
                # 使用输出目录 + 自动生成文件名
                output_dir = Path(output_directory)

                if use_timestamp:
                    # 生成带时间戳的文件名
                    prefix = file_naming.get('prefix', 'filtered_messages')
                    extension = file_naming.get('extension', '.json')
                    timestamp_format = file_naming.get('timestamp_format', '%Y%m%d_%H%M%S')
                    timestamp = datetime.now().strftime(timestamp_format)
                    filename = f"{prefix}_{timestamp}{extension}"
                else:
                    # 使用配置中的file_path作为文件名
                    filename = self.config.get('file_path', 'filtered_messages.json')
                    # 如果file_path包含路径，只取文件名部分
                    filename = os.path.basename(filename)

                self.file_path = str(output_dir / filename)

            else:
                # 使用配置中的file_path
                self.file_path = self.config.get('file_path')

            self.logger.info(f"生成的输出文件路径: {self.file_path}")

        except Exception as e:
            self.logger.error(f"生成文件路径失败: {e}")
            raise RuntimeError(f"文件路径生成失败: {e}")

    def _init_file_output(self) -> None:
        """初始化文件输出"""
        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(self.file_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)
                self.logger.info(f"创建输出目录: {output_dir}")

            # 检查目录权限
            if output_dir and not os.access(output_dir, os.W_OK):
                raise PermissionError(f"没有写入权限: {output_dir}")

            # 根据输出格式初始化文件
            if self.output_format == OutputFormat.JSON_ARRAY:
                self._init_json_array_file()
            else:
                # 传统文件输出方式
                self.file_handle = open(self.file_path, 'w', encoding='utf-8')
                self.logger.info(f"文件输出已初始化: {self.file_path}")

        except Exception as e:
            self.logger.error(f"初始化文件输出失败: {e}")
            raise RuntimeError(f"文件输出初始化失败: {e}")

    def _init_json_array_file(self) -> None:
        """初始化JSON数组文件"""
        try:
            # 检查文件是否存在以及是否需要创建新文件
            file_exists = os.path.exists(self.file_path)

            if self.new_file_on_startup or not file_exists:
                # 创建新文件，初始化为空JSON数组
                with open(self.file_path, 'w', encoding='utf-8') as f:
                    f.write('[]')
                self.logger.info(f"创建新的JSON数组文件: {self.file_path}")
            else:
                # 验证现有文件是否为有效的JSON数组
                if not self._validate_json_array_file():
                    self.logger.warning(f"现有文件不是有效的JSON数组，将重新创建: {self.file_path}")
                    with open(self.file_path, 'w', encoding='utf-8') as f:
                        f.write('[]')
                else:
                    self.logger.info(f"使用现有的JSON数组文件: {self.file_path}")

        except Exception as e:
            self.logger.error(f"初始化JSON数组文件失败: {e}")
            raise RuntimeError(f"JSON数组文件初始化失败: {e}")

    def _validate_json_array_file(self) -> bool:
        """验证JSON数组文件的有效性"""
        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if not content:
                    return False

                # 尝试解析为JSON数组
                data = json.loads(content)
                return isinstance(data, list)

        except (json.JSONDecodeError, FileNotFoundError, PermissionError):
            return False
    
    def output_message(self, data: dict, filter_details: Optional[Dict[str, Any]] = None) -> None:
        """
        输出单条消息

        参数:
            data: 要输出的消息数据
            filter_details: 过滤详情（可选）
        """
        try:
            # 控制台输出
            if self.output_type in [OutputType.CONSOLE, OutputType.BOTH]:
                output_content = self._format_message(data, filter_details)
                print(output_content)

            # 文件输出
            if self.output_type in [OutputType.FILE, OutputType.BOTH]:
                if self.output_format == OutputFormat.JSON_ARRAY:
                    self._output_to_json_array(data, filter_details)
                elif self.file_handle:
                    output_content = self._format_message(data, filter_details)
                    self.file_handle.write(output_content + '\n')
                    self.file_handle.flush()

            self.output_count += 1

        except Exception as e:
            self.logger.error(f"输出消息失败: {e}")

    def _output_to_json_array(self, data: dict, filter_details: Optional[Dict[str, Any]] = None) -> None:
        """
        输出消息到JSON数组文件

        参数:
            data: 要输出的消息数据
            filter_details: 过滤详情（可选）
        """
        try:
            # 准备要写入的消息数据
            message_data = data.copy()

            # 添加过滤详情（如果需要）
            if filter_details and self.config.get('show_filter_details', False):
                message_data['_filter_details'] = filter_details

            # 添加输出时间戳
            message_data['_output_timestamp'] = datetime.now().isoformat()

            # 添加到批量缓存
            self.json_array_messages.append(message_data)

            # 检查是否需要写入文件
            if len(self.json_array_messages) >= self.batch_size:
                self._flush_json_array_messages()

        except Exception as e:
            self.logger.error(f"输出到JSON数组失败: {e}")

    def _flush_json_array_messages(self) -> None:
        """将缓存的消息写入JSON数组文件"""
        if not self.json_array_messages:
            return

        try:
            # 读取现有的JSON数组
            existing_data = []
            if os.path.exists(self.file_path):
                with open(self.file_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    if content:
                        existing_data = json.loads(content)

            # 添加新消息
            existing_data.extend(self.json_array_messages)

            # 原子性写入：先写入临时文件，然后重命名
            temp_file = self.file_path + '.tmp'
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(existing_data, f, ensure_ascii=False, indent=2)

            # 原子性替换文件
            shutil.move(temp_file, self.file_path)

            self.logger.debug(f"成功写入 {len(self.json_array_messages)} 条消息到JSON数组文件")

            # 清空缓存
            self.json_array_messages.clear()

        except Exception as e:
            self.logger.error(f"写入JSON数组文件失败: {e}")
            # 清理临时文件
            temp_file = self.file_path + '.tmp'
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except:
                    pass
    
    def output_messages_batch(self, messages: List[dict], 
                            filter_details_list: Optional[List[Dict[str, Any]]] = None) -> None:
        """
        批量输出消息
        
        参数:
            messages: 消息列表
            filter_details_list: 过滤详情列表（可选）
        """
        for i, message in enumerate(messages):
            filter_details = filter_details_list[i] if filter_details_list else None
            self.output_message(message, filter_details)
    
    def _format_message(self, data: dict, filter_details: Optional[Dict[str, Any]] = None) -> str:
        """
        格式化消息内容

        参数:
            data: 消息数据
            filter_details: 过滤详情

        返回:
            格式化后的字符串
        """
        if self.output_format == OutputFormat.JSON:
            return json.dumps(data, ensure_ascii=False, separators=(',', ':'))

        elif self.output_format == OutputFormat.PRETTY_JSON:
            return json.dumps(data, ensure_ascii=False, indent=2)

        elif self.output_format == OutputFormat.JSON_ARRAY:
            # JSON数组格式在控制台输出时使用紧凑JSON格式
            return json.dumps(data, ensure_ascii=False, separators=(',', ':'))

        elif self.output_format == OutputFormat.TEXT:
            return self._format_as_text(data, filter_details)

        else:
            return str(data)
    
    def _format_as_text(self, data: dict, filter_details: Optional[Dict[str, Any]] = None) -> str:
        """
        将消息格式化为文本格式
        
        参数:
            data: 消息数据
            filter_details: 过滤详情
            
        返回:
            文本格式字符串
        """
        lines = []
        
        # 添加时间戳
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        lines.append(f"[{timestamp}] 过滤消息 #{self.output_count + 1}")
        lines.append("-" * 50)
        
        # 添加消息内容
        lines.append("消息内容:")
        for key, value in data.items():
            if key.startswith('_kafka_metadata'):
                continue
            lines.append(f"  {key}: {value}")
        
        # 添加Kafka元数据
        if '_kafka_metadata' in data:
            metadata = data['_kafka_metadata']
            lines.append("\nKafka元数据:")
            lines.append(f"  Topic: {metadata.get('topic')}")
            lines.append(f"  Partition: {metadata.get('partition')}")
            lines.append(f"  Offset: {metadata.get('offset')}")
            lines.append(f"  Timestamp: {metadata.get('timestamp')}")
        
        # 添加过滤详情
        if filter_details and self.config.get('show_filter_details', False):
            lines.append("\n过滤详情:")
            for rule_result in filter_details.get('rule_results', []):
                status = "通过" if rule_result['passed'] else "未通过"
                lines.append(f"  规则 {rule_result['index']}: {status}")
                if 'error' in rule_result:
                    lines.append(f"    错误: {rule_result['error']}")
        
        lines.append("")  # 空行分隔
        
        return '\n'.join(lines)
    
    def output_statistics(self, statistics: Dict[str, Any]) -> None:
        """
        输出统计信息
        
        参数:
            statistics: 统计信息字典
        """
        try:
            stats_content = self._format_statistics(statistics)
            
            # 控制台输出
            if self.output_type in [OutputType.CONSOLE, OutputType.BOTH]:
                print("\n" + "=" * 60)
                print("过滤统计信息")
                print("=" * 60)
                print(stats_content)
                print("=" * 60)
            
            # 文件输出
            if self.output_type in [OutputType.FILE, OutputType.BOTH] and self.file_handle:
                self.file_handle.write(f"\n{'='*60}\n")
                self.file_handle.write("过滤统计信息\n")
                self.file_handle.write(f"{'='*60}\n")
                self.file_handle.write(stats_content + '\n')
                self.file_handle.write(f"{'='*60}\n")
                self.file_handle.flush()
            
        except Exception as e:
            self.logger.error(f"输出统计信息失败: {e}")
    
    def _format_statistics(self, statistics: Dict[str, Any]) -> str:
        """
        格式化统计信息
        
        参数:
            statistics: 统计信息字典
            
        返回:
            格式化后的统计信息字符串
        """
        lines = []
        
        # 基本统计
        lines.append(f"总处理消息数: {statistics.get('total_processed', 0)}")
        lines.append(f"通过过滤消息数: {statistics.get('total_passed', 0)}")
        lines.append(f"被过滤消息数: {statistics.get('total_filtered', 0)}")
        lines.append(f"通过率: {statistics.get('pass_rate', 0)}%")
        lines.append(f"规则数量: {statistics.get('rule_count', 0)}")
        lines.append(f"输出消息数: {self.output_count}")
        
        # 规则统计
        rule_stats = statistics.get('rule_statistics', {})
        if rule_stats:
            lines.append("\n规则详细统计:")
            for rule_index, rule_stat in rule_stats.items():
                lines.append(f"  规则 {rule_index}:")
                lines.append(f"    规则内容: {rule_stat.get('rule', 'N/A')}")
                lines.append(f"    处理数: {rule_stat.get('processed', 0)}")
                lines.append(f"    通过数: {rule_stat.get('passed', 0)}")
                lines.append(f"    失败数: {rule_stat.get('failed', 0)}")
                
                processed = rule_stat.get('processed', 0)
                if processed > 0:
                    pass_rate = rule_stat.get('passed', 0) / processed * 100
                    lines.append(f"    通过率: {pass_rate:.2f}%")
        
        return '\n'.join(lines)
    
    def close(self) -> None:
        """关闭输出处理器"""
        if self.file_handle:
            try:
                self.file_handle.close()
                self.logger.info("文件输出已关闭")
            except Exception as e:
                self.logger.error(f"关闭文件输出失败: {e}")
            finally:
                self.file_handle = None
    
    def get_output_count(self) -> int:
        """
        获取输出消息数量
        
        返回:
            输出消息数量
        """
        return self.output_count
    
    def reset_count(self) -> None:
        """重置输出计数"""
        self.output_count = 0
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()
