#!/usr/bin/env python3
"""
Kafka消费者组管理器

提供消费者组的创建、监控、停止和删除功能
包含安全检查和数据一致性保障
"""

import json
import time
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from kafka import KafkaConsumer, KafkaAdminClient
from kafka.admin import ConfigResource, ConfigResourceType
from kafka.structs import TopicPartition, OffsetAndMetadata
from kafka.errors import KafkaError, GroupCoordinatorNotAvailableError


class KafkaConsumerGroupManager:
    """Kafka消费者组管理器"""
    
    def __init__(self, bootstrap_servers: str, **kwargs):
        """
        初始化消费者组管理器
        
        参数:
            bootstrap_servers: Kafka集群地址
            **kwargs: 其他Kafka配置参数
        """
        self.bootstrap_servers = bootstrap_servers
        self.kafka_config = {
            'bootstrap_servers': bootstrap_servers,
            **kwargs
        }
        self.admin_client = None
        self.logger = logging.getLogger(__name__)
        
        # 初始化管理客户端
        self._init_admin_client()
    
    def _init_admin_client(self) -> None:
        """初始化Kafka管理客户端"""
        try:
            # 过滤出AdminClient支持的配置参数
            admin_config = self._filter_admin_config(self.kafka_config)
            self.admin_client = KafkaAdminClient(**admin_config)
            self.logger.info(f"成功连接到Kafka集群: {self.bootstrap_servers}")
        except Exception as e:
            self.logger.error(f"连接Kafka集群失败: {e}")
            raise RuntimeError(f"无法连接到Kafka集群: {e}")

    def _filter_admin_config(self, kafka_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        过滤出AdminClient支持的配置参数

        参数:
            kafka_config: 原始Kafka配置

        返回:
            AdminClient支持的配置字典
        """
        # AdminClient支持的配置参数列表
        admin_supported_configs = {
            'bootstrap_servers',
            'client_id',
            'request_timeout_ms',
            'connections_max_idle_ms',
            'retry_backoff_ms',
            'max_in_flight_requests_per_connection',
            'receive_buffer_bytes',
            'send_buffer_bytes',
            'socket_options',
            'sock_chunk_bytes',
            'sock_chunk_buffer_size',
            'reconnect_backoff_ms',
            'reconnect_backoff_max_ms',
            'max_in_flight_requests_per_connection',
            'security_protocol',
            'ssl_check_hostname',
            'ssl_context',
            'ssl_keyfile',
            'ssl_certfile',
            'ssl_cafile',
            'ssl_cadata',
            'ssl_ciphers',
            'ssl_password',
            'sasl_mechanism',
            'sasl_plain_username',
            'sasl_plain_password',
            'sasl_kerberos_service_name',
            'sasl_kerberos_domain_name',
            'sasl_oauth_token_provider',
            'api_version',
            'api_version_auto_timeout_ms',
            'metric_reporters',
            'metrics_num_samples',
            'metrics_sample_window_ms'
        }

        # 过滤配置
        admin_config = {}
        for key, value in kafka_config.items():
            if key in admin_supported_configs:
                admin_config[key] = value
            else:
                self.logger.debug(f"跳过AdminClient不支持的配置: {key}")

        return admin_config
    
    def get_consumer_group_info(self, group_id: str, retry_count: int = 3,
                               retry_delay: float = 1.0) -> Dict[str, Any]:
        """
        获取消费者组详细信息

        参数:
            group_id: 消费者组ID
            retry_count: 重试次数
            retry_delay: 重试间隔（秒）

        返回:
            消费者组信息字典
        """
        last_error = None

        for attempt in range(retry_count):
            try:
                self.logger.debug(f"获取消费者组信息，尝试 {attempt + 1}/{retry_count}")

                # 获取消费者组元数据
                group_metadata_raw = self.admin_client.describe_consumer_groups([group_id])

                # 调试：打印返回数据的类型和结构
                self.logger.debug(f"describe_consumer_groups 返回类型: {type(group_metadata_raw)}")
                self.logger.debug(f"describe_consumer_groups 返回内容: {group_metadata_raw}")

                # 处理不同的返回格式
                group_metadata, group_info = self._parse_describe_groups_result(group_metadata_raw, group_id)

                if group_metadata is None or group_info is None:
                    self.logger.debug(f"消费者组 {group_id} 不在返回结果中或解析失败")
                    # 如果不是最后一次尝试，等待后重试
                    if attempt < retry_count - 1:
                        self.logger.debug(f"等待 {retry_delay} 秒后重试...")
                        time.sleep(retry_delay)
                        continue
                    return {'exists': False, 'group_id': group_id, 'reason': 'not_in_metadata_or_parse_failed'}
                self.logger.debug(f"消费者组 {group_id} 状态: {group_info.state}")

                # 检查是否有错误
                if hasattr(group_info, 'error') and group_info.error:
                    error_msg = str(group_info.error)
                    self.logger.debug(f"消费者组 {group_id} 有错误: {error_msg}")

                    # 如果是"组不存在"的错误，直接返回
                    if 'does not exist' in error_msg.lower() or 'unknown group' in error_msg.lower():
                        return {'exists': False, 'group_id': group_id, 'reason': 'group_not_exist', 'error': error_msg}

                    # 其他错误，如果不是最后一次尝试，等待后重试
                    if attempt < retry_count - 1:
                        self.logger.debug(f"遇到错误，等待 {retry_delay} 秒后重试: {error_msg}")
                        time.sleep(retry_delay)
                        continue

                    return {'exists': False, 'group_id': group_id, 'reason': 'error', 'error': error_msg}

                # 获取消费者组的offset信息
                offset_info = self._get_group_offsets(group_id)

                # 获取消费者组成员信息
                members_info = self._get_group_members(group_info)

                result = {
                    'exists': True,
                    'group_id': group_id,
                    'state': group_info.state,
                    'protocol_type': group_info.protocol_type,
                    'protocol': group_info.protocol,
                    'members': members_info,
                    'member_count': len(members_info),
                    'coordinator': {
                        'id': group_info.coordinator.nodeId,
                        'host': group_info.coordinator.host,
                        'port': group_info.coordinator.port
                    },
                    'offsets': offset_info,
                    'last_updated': datetime.now().isoformat(),
                    'attempt': attempt + 1
                }

                self.logger.debug(f"成功获取消费者组 {group_id} 信息")
                return result

            except Exception as e:
                last_error = e
                error_msg = str(e)
                self.logger.debug(f"kafka-python-ng: 获取消费者组信息失败 (尝试 {attempt + 1}/{retry_count}): {error_msg}")

                # kafka-python-ng 特定错误处理
                if "'list' object has no attribute 'keys'" in error_msg:
                    self.logger.warning("kafka-python-ng: 检测到数据类型兼容性问题，这是已知的kafka-python-ng问题")
                elif "Future" in error_msg or "result" in error_msg:
                    self.logger.warning("kafka-python-ng: 检测到异步结果处理问题")
                elif "attribute" in error_msg and "object" in error_msg:
                    self.logger.warning("kafka-python-ng: 检测到对象属性访问问题")

                # 如果不是最后一次尝试，等待后重试
                if attempt < retry_count - 1:
                    self.logger.debug(f"等待 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
                    continue

        # 所有尝试都失败了
        error_msg = str(last_error) if last_error else "未知错误"
        self.logger.error(f"获取消费者组信息失败，已重试 {retry_count} 次: {error_msg}")

        # 提供更详细的错误信息
        if "'list' object has no attribute 'keys'" in error_msg:
            self.logger.error("检测到数据类型错误，这可能是kafka-python库版本兼容性问题")
            self.logger.error("建议检查kafka-python库版本或联系技术支持")

        return {
            'exists': False,
            'error': error_msg,
            'group_id': group_id,
            'reason': 'exception',
            'retry_count': retry_count
        }

    def consumer_group_exists(self, group_id: str, retry_count: int = 3,
                            retry_delay: float = 1.0) -> Tuple[bool, str]:
        """
        检查消费者组是否存在（轻量级检查）

        参数:
            group_id: 消费者组ID
            retry_count: 重试次数
            retry_delay: 重试间隔（秒）

        返回:
            (是否存在, 详细信息)
        """
        try:
            # 首先尝试列出所有消费者组
            all_groups = self.admin_client.list_consumer_groups()

            # 调试：打印返回数据的类型和结构
            self.logger.debug(f"list_consumer_groups 返回类型: {type(all_groups)}")
            if all_groups:
                self.logger.debug(f"第一个元素类型: {type(all_groups[0])}")
                self.logger.debug(f"第一个元素内容: {all_groups[0]}")

            # 兼容不同版本的kafka-python库返回格式
            group_ids = self._extract_group_ids(all_groups)

            self.logger.debug(f"当前所有消费者组: {group_ids}")

            if group_id in group_ids:
                self.logger.debug(f"消费者组 {group_id} 在列表中找到")
                return True, "found_in_list"
            else:
                self.logger.debug(f"消费者组 {group_id} 不在列表中")

                # 再次尝试describe方法确认
                group_info = self.get_consumer_group_info(group_id, retry_count, retry_delay)
                exists = group_info.get('exists', False)
                reason = group_info.get('reason', 'unknown')

                return exists, f"describe_result_{reason}"

        except Exception as e:
            self.logger.error(f"检查消费者组存在性失败: {e}")
            return False, f"error_{str(e)}"

    def _extract_group_ids(self, all_groups) -> List[str]:
        """
        从list_consumer_groups的返回结果中提取消费者组ID列表
        专门针对kafka-python-ng库优化

        参数:
            all_groups: list_consumer_groups的返回结果

        返回:
            消费者组ID列表
        """
        group_ids = []

        try:
            self.logger.debug(f"kafka-python-ng: 提取group_ids，输入类型: {type(all_groups)}")

            if not all_groups:
                self.logger.debug("kafka-python-ng: all_groups为空")
                return group_ids

            # kafka-python-ng 特殊处理: 检查是否需要解包结果
            if hasattr(all_groups, 'result'):
                self.logger.debug("kafka-python-ng: 检测到Future对象，获取结果")
                try:
                    all_groups = all_groups.result()
                except Exception as e:
                    self.logger.error(f"获取Future结果失败: {e}")
                    return group_ids

            if hasattr(all_groups, 'value'):
                self.logger.debug("kafka-python-ng: 检测到包装对象，提取value")
                all_groups = all_groups.value

            # 确保all_groups是可迭代的
            if not hasattr(all_groups, '__iter__') or isinstance(all_groups, (str, bytes)):
                self.logger.warning(f"kafka-python-ng: all_groups不是可迭代对象: {type(all_groups)}")
                return group_ids

            for i, group in enumerate(all_groups):
                self.logger.debug(f"kafka-python-ng: 处理group {i}, 类型: {type(group)}")

                group_id = None

                # kafka-python-ng 方法1: 使用专门的提取方法
                group_id = self._extract_group_id_from_info_ng(group)

                if group_id:
                    group_ids.append(str(group_id))
                    self.logger.debug(f"kafka-python-ng: 成功提取group_id: {group_id}")
                else:
                    self.logger.warning(f"kafka-python-ng: 无法从以下数据中提取group_id: {group} (类型: {type(group)})")

                    # 额外的调试信息
                    if hasattr(group, '__dict__'):
                        self.logger.debug(f"对象属性: {group.__dict__}")

            self.logger.debug(f"kafka-python-ng: 最终提取的group_ids: {group_ids}")
            return group_ids

        except Exception as e:
            self.logger.error(f"kafka-python-ng: 提取消费者组ID时发生错误: {e}")
            import traceback
            self.logger.debug(f"详细错误信息: {traceback.format_exc()}")
            return []

    def _parse_describe_groups_result(self, group_metadata_raw, group_id: str):
        """
        解析describe_consumer_groups的返回结果
        专门针对kafka-python-ng库优化，兼容其特定的返回格式

        参数:
            group_metadata_raw: describe_consumer_groups的原始返回结果
            group_id: 目标消费者组ID

        返回:
            (group_metadata_dict, group_info) 或 (None, None)
        """
        try:
            self.logger.debug(f"kafka-python-ng 解析: 原始数据类型 {type(group_metadata_raw)}")

            # kafka-python-ng 特殊情况1: 返回Future对象或类似的异步结果
            if hasattr(group_metadata_raw, 'result'):
                self.logger.debug("检测到Future对象，尝试获取结果")
                try:
                    group_metadata_raw = group_metadata_raw.result()
                    self.logger.debug(f"Future结果类型: {type(group_metadata_raw)}")
                except Exception as e:
                    self.logger.error(f"获取Future结果失败: {e}")
                    return None, None

            # kafka-python-ng 特殊情况2: 返回结果可能包装在特殊对象中
            if hasattr(group_metadata_raw, 'value'):
                self.logger.debug("检测到包装对象，提取value")
                group_metadata_raw = group_metadata_raw.value

            # 情况1: 返回结果是字典格式 {group_id: group_info}
            if isinstance(group_metadata_raw, dict):
                self.logger.debug("kafka-python-ng: 返回格式为字典")
                if group_id in group_metadata_raw:
                    group_info = group_metadata_raw[group_id]
                    self.logger.debug(f"找到消费者组 {group_id}, 信息类型: {type(group_info)}")
                    return group_metadata_raw, group_info
                else:
                    self.logger.debug(f"字典中不包含消费者组 {group_id}, 可用键: {list(group_metadata_raw.keys())}")
                    return None, None

            # 情况2: 返回结果是列表格式 [group_info1, group_info2, ...]
            elif isinstance(group_metadata_raw, list):
                self.logger.debug(f"kafka-python-ng: 返回格式为列表，长度: {len(group_metadata_raw)}")

                for i, group_info in enumerate(group_metadata_raw):
                    self.logger.debug(f"处理列表元素 {i}: {type(group_info)}")

                    # 尝试从group_info中提取group_id
                    extracted_group_id = self._extract_group_id_from_info_ng(group_info)

                    self.logger.debug(f"从group_info中提取的group_id: {extracted_group_id}")

                    if extracted_group_id == group_id:
                        # 构造字典格式以保持兼容性
                        group_metadata_dict = {group_id: group_info}
                        self.logger.debug(f"成功匹配消费者组 {group_id}")
                        return group_metadata_dict, group_info

                self.logger.debug(f"列表中不包含消费者组 {group_id}")
                return None, None

            # 情况3: kafka-python-ng 可能返回特殊的响应对象
            elif hasattr(group_metadata_raw, '__iter__') and not isinstance(group_metadata_raw, (str, bytes)):
                self.logger.debug("kafka-python-ng: 检测到可迭代对象")
                try:
                    items = list(group_metadata_raw)
                    self.logger.debug(f"转换为列表，长度: {len(items)}")
                    return self._parse_describe_groups_result(items, group_id)
                except Exception as e:
                    self.logger.error(f"转换可迭代对象失败: {e}")
                    return None, None

            # 情况4: 单个group_info对象
            else:
                self.logger.debug("kafka-python-ng: 尝试作为单个group_info对象处理")
                extracted_group_id = self._extract_group_id_from_info_ng(group_metadata_raw)

                if extracted_group_id == group_id:
                    group_metadata_dict = {group_id: group_metadata_raw}
                    return group_metadata_dict, group_metadata_raw
                else:
                    self.logger.debug(f"单个对象的group_id不匹配: {extracted_group_id} != {group_id}")
                    return None, None

        except Exception as e:
            self.logger.error(f"kafka-python-ng 解析describe_consumer_groups结果时发生错误: {e}")
            import traceback
            self.logger.debug(f"详细错误信息: {traceback.format_exc()}")
            return None, None

    def _extract_group_id_from_info(self, group_info) -> Optional[str]:
        """
        从group_info对象中提取group_id (通用方法)

        参数:
            group_info: 消费者组信息对象

        返回:
            提取的group_id或None
        """
        return self._extract_group_id_from_info_ng(group_info)

    def _extract_group_id_from_info_ng(self, group_info) -> Optional[str]:
        """
        从group_info对象中提取group_id (专门针对kafka-python-ng优化)

        参数:
            group_info: 消费者组信息对象

        返回:
            提取的group_id或None
        """
        try:
            self.logger.debug(f"kafka-python-ng: 提取group_id，对象类型: {type(group_info)}")

            # kafka-python-ng 特殊情况1: 检查是否有error属性先
            if hasattr(group_info, 'error') and group_info.error:
                self.logger.debug(f"group_info包含错误: {group_info.error}")
                # 即使有错误，也尝试提取group_id

            # kafka-python-ng 方法1: 直接访问group_id属性
            if hasattr(group_info, 'group_id'):
                group_id = group_info.group_id
                self.logger.debug(f"通过group_id属性获取: {group_id}")
                return str(group_id) if group_id is not None else None

            # kafka-python-ng 方法2: 访问group属性
            if hasattr(group_info, 'group'):
                group_id = group_info.group
                self.logger.debug(f"通过group属性获取: {group_id}")
                return str(group_id) if group_id is not None else None

            # kafka-python-ng 方法3: 检查是否有name属性（某些版本可能使用）
            if hasattr(group_info, 'name'):
                group_id = group_info.name
                self.logger.debug(f"通过name属性获取: {group_id}")
                return str(group_id) if group_id is not None else None

            # kafka-python-ng 方法4: 如果是字典格式
            if isinstance(group_info, dict):
                for key in ['group_id', 'group', 'groupId', 'name', 'id']:
                    if key in group_info and group_info[key] is not None:
                        group_id = group_info[key]
                        self.logger.debug(f"通过字典键'{key}'获取: {group_id}")
                        return str(group_id)

            # kafka-python-ng 方法5: 如果是tuple格式
            if isinstance(group_info, tuple) and len(group_info) > 0:
                group_id = group_info[0]
                self.logger.debug(f"通过tuple[0]获取: {group_id}")
                return str(group_id) if group_id is not None else None

            # kafka-python-ng 方法6: 尝试其他可能的属性名
            for attr in ['groupId', 'consumer_group_id', 'id', 'identifier']:
                if hasattr(group_info, attr):
                    group_id = getattr(group_info, attr)
                    if group_id is not None:
                        self.logger.debug(f"通过属性'{attr}'获取: {group_id}")
                        return str(group_id)

            # kafka-python-ng 方法7: 如果对象有__dict__，检查所有属性
            if hasattr(group_info, '__dict__'):
                self.logger.debug(f"对象属性: {group_info.__dict__}")
                for key, value in group_info.__dict__.items():
                    if 'group' in key.lower() and value is not None:
                        self.logger.debug(f"通过__dict__中的'{key}'获取: {value}")
                        return str(value)

            # kafka-python-ng 方法8: 如果是字符串（直接就是group_id）
            if isinstance(group_info, str):
                self.logger.debug(f"直接使用字符串作为group_id: {group_info}")
                return group_info

            self.logger.warning(f"kafka-python-ng: 无法从group_info中提取group_id")
            self.logger.warning(f"对象类型: {type(group_info)}")
            self.logger.warning(f"对象内容: {group_info}")
            if hasattr(group_info, '__dict__'):
                self.logger.warning(f"对象属性: {group_info.__dict__}")

            return None

        except Exception as e:
            self.logger.error(f"kafka-python-ng: 提取group_id时发生错误: {e}")
            import traceback
            self.logger.debug(f"详细错误信息: {traceback.format_exc()}")
            return None

    def consumer_group_exists_fallback(self, group_id: str) -> Tuple[bool, str]:
        """
        备用的消费者组存在性检查方法
        直接使用describe_consumer_groups方法

        参数:
            group_id: 消费者组ID

        返回:
            (是否存在, 详细信息)
        """
        try:
            self.logger.debug(f"使用备用方法检查消费者组 {group_id}")

            # 直接尝试describe消费者组
            group_metadata_raw = self.admin_client.describe_consumer_groups([group_id])

            # 使用相同的解析逻辑
            group_metadata, group_info = self._parse_describe_groups_result(group_metadata_raw, group_id)

            if group_metadata is not None and group_info is not None:
                # 检查是否有错误
                if hasattr(group_info, 'error') and group_info.error:
                    error_msg = str(group_info.error)
                    if 'does not exist' in error_msg.lower() or 'unknown group' in error_msg.lower():
                        return False, f"fallback_not_exist_{error_msg}"
                    else:
                        return False, f"fallback_error_{error_msg}"
                else:
                    state = getattr(group_info, 'state', 'Unknown')
                    return True, f"fallback_exists_{state}"
            else:
                return False, "fallback_not_in_metadata"

        except Exception as e:
            self.logger.debug(f"备用检查方法也失败: {e}")
            return False, f"fallback_exception_{str(e)}"
    
    def _get_group_offsets(self, group_id: str) -> Dict[str, Any]:
        """
        获取消费者组的offset信息

        参数:
            group_id: 消费者组ID

        返回:
            offset信息字典
        """
        try:
            # 创建临时消费者获取offset信息
            # 只使用消费者支持的配置参数
            consumer_config = {
                'group_id': group_id,
                'bootstrap_servers': self.bootstrap_servers,
                'enable_auto_commit': False,
                'auto_offset_reset': 'latest'
            }

            # 添加安全相关配置（如果存在）
            security_configs = ['security_protocol', 'sasl_mechanism',
                              'sasl_plain_username', 'sasl_plain_password']
            for config_key in security_configs:
                if config_key in self.kafka_config:
                    consumer_config[config_key] = self.kafka_config[config_key]

            temp_consumer = KafkaConsumer(**consumer_config)
            
            # 获取分配的分区
            partitions = temp_consumer.assignment()
            
            offset_info = {}
            for partition in partitions:
                try:
                    # 获取当前offset
                    current_offset = temp_consumer.position(partition)
                    
                    # 获取最新offset
                    end_offset = temp_consumer.end_offsets([partition])[partition]
                    
                    # 计算lag
                    lag = end_offset - current_offset if current_offset is not None else None
                    
                    offset_info[f"{partition.topic}-{partition.partition}"] = {
                        'topic': partition.topic,
                        'partition': partition.partition,
                        'current_offset': current_offset,
                        'end_offset': end_offset,
                        'lag': lag
                    }
                except Exception as e:
                    self.logger.warning(f"获取分区{partition}的offset失败: {e}")
            
            temp_consumer.close()
            return offset_info
            
        except Exception as e:
            self.logger.error(f"获取消费者组offset信息失败: {e}")
            return {}
    
    def _get_group_members(self, group_info) -> List[Dict[str, Any]]:
        """
        获取消费者组成员信息
        
        参数:
            group_info: 消费者组信息对象
            
        返回:
            成员信息列表
        """
        members = []
        for member in group_info.members:
            member_info = {
                'member_id': member.member_id,
                'client_id': member.client_id,
                'client_host': member.client_host,
                'member_metadata': self._parse_member_metadata(member.member_metadata),
                'member_assignment': self._parse_member_assignment(member.member_assignment)
            }
            members.append(member_info)
        
        return members
    
    def _parse_member_metadata(self, metadata: bytes) -> Dict[str, Any]:
        """解析成员元数据"""
        try:
            # 这里简化处理，实际可能需要更复杂的解析
            return {'raw_size': len(metadata) if metadata else 0}
        except Exception:
            return {'raw_size': 0}
    
    def _parse_member_assignment(self, assignment: bytes) -> Dict[str, Any]:
        """解析成员分配信息"""
        try:
            # 这里简化处理，实际可能需要更复杂的解析
            return {'raw_size': len(assignment) if assignment else 0}
        except Exception:
            return {'raw_size': 0}
    
    def check_group_safety_for_deletion(self, group_id: str) -> Tuple[bool, List[str]]:
        """
        检查消费者组是否可以安全删除
        
        参数:
            group_id: 消费者组ID
            
        返回:
            (是否安全, 警告信息列表)
        """
        warnings = []
        
        try:
            group_info = self.get_consumer_group_info(group_id)
            
            if not group_info.get('exists', False):
                return True, ['消费者组不存在']
            
            # 检查消费者组状态
            state = group_info.get('state', 'Unknown')
            if state not in ['Empty', 'Dead']:
                warnings.append(f"消费者组状态为 {state}，建议等待状态变为 Empty 或 Dead")
            
            # 检查活跃成员
            member_count = group_info.get('member_count', 0)
            if member_count > 0:
                warnings.append(f"消费者组仍有 {member_count} 个活跃成员")
            
            # 检查offset lag
            offsets = group_info.get('offsets', {})
            high_lag_partitions = []
            for partition_key, offset_data in offsets.items():
                lag = offset_data.get('lag')
                if lag is not None and lag > 1000:  # 可配置的阈值
                    high_lag_partitions.append(f"{partition_key}(lag: {lag})")
            
            if high_lag_partitions:
                warnings.append(f"以下分区存在较大lag: {', '.join(high_lag_partitions)}")
            
            # 判断是否安全
            is_safe = len(warnings) == 0 or (
                state in ['Empty', 'Dead'] and member_count == 0
            )
            
            return is_safe, warnings
            
        except Exception as e:
            self.logger.error(f"检查消费者组安全性失败: {e}")
            return False, [f"检查失败: {str(e)}"]
    
    def backup_consumer_group_offsets(self, group_id: str, backup_path: str = None) -> str:
        """
        备份消费者组的offset信息
        
        参数:
            group_id: 消费者组ID
            backup_path: 备份文件路径，默认自动生成
            
        返回:
            备份文件路径
        """
        if backup_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"consumer_group_backup_{group_id}_{timestamp}.json"
        
        try:
            group_info = self.get_consumer_group_info(group_id)
            
            backup_data = {
                'group_id': group_id,
                'backup_time': datetime.now().isoformat(),
                'group_info': group_info,
                'kafka_cluster': self.bootstrap_servers
            }
            
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"消费者组 {group_id} 的信息已备份到: {backup_path}")
            return backup_path
            
        except Exception as e:
            self.logger.error(f"备份消费者组信息失败: {e}")
            raise RuntimeError(f"备份失败: {e}")

    def wait_for_group_empty(self, group_id: str, timeout_seconds: int = 300,
                           check_interval: int = 5) -> bool:
        """
        等待消费者组变为空状态

        参数:
            group_id: 消费者组ID
            timeout_seconds: 超时时间（秒）
            check_interval: 检查间隔（秒）

        返回:
            是否成功等待到空状态
        """
        start_time = time.time()

        self.logger.info(f"等待消费者组 {group_id} 变为空状态...")

        while time.time() - start_time < timeout_seconds:
            try:
                group_info = self.get_consumer_group_info(group_id)

                if not group_info.get('exists', False):
                    self.logger.info(f"消费者组 {group_id} 不存在")
                    return True

                state = group_info.get('state', 'Unknown')
                member_count = group_info.get('member_count', 0)

                if state in ['Empty', 'Dead'] and member_count == 0:
                    self.logger.info(f"消费者组 {group_id} 已变为空状态")
                    return True

                self.logger.info(f"消费者组 {group_id} 状态: {state}, 成员数: {member_count}")
                time.sleep(check_interval)

            except Exception as e:
                self.logger.warning(f"检查消费者组状态时出错: {e}")
                time.sleep(check_interval)

        self.logger.warning(f"等待消费者组 {group_id} 变为空状态超时")
        return False

    def delete_consumer_group(self, group_id: str, force: bool = False,
                            backup: bool = True) -> bool:
        """
        删除消费者组

        参数:
            group_id: 消费者组ID
            force: 是否强制删除（跳过安全检查）
            backup: 是否在删除前备份

        返回:
            是否删除成功
        """
        try:
            self.logger.info(f"开始删除消费者组: {group_id}")

            # 检查消费者组是否存在
            group_info = self.get_consumer_group_info(group_id)
            if not group_info.get('exists', False):
                self.logger.info(f"消费者组 {group_id} 不存在，无需删除")
                return True

            # 备份消费者组信息
            if backup:
                try:
                    backup_path = self.backup_consumer_group_offsets(group_id)
                    self.logger.info(f"已备份消费者组信息到: {backup_path}")
                except Exception as e:
                    self.logger.warning(f"备份失败，但继续删除操作: {e}")

            # 安全检查
            if not force:
                is_safe, warnings = self.check_group_safety_for_deletion(group_id)
                if not is_safe:
                    self.logger.error(f"消费者组 {group_id} 不满足安全删除条件:")
                    for warning in warnings:
                        self.logger.error(f"  - {warning}")
                    self.logger.error("使用 force=True 参数可强制删除")
                    return False

                if warnings:
                    self.logger.warning("删除前检查发现以下警告:")
                    for warning in warnings:
                        self.logger.warning(f"  - {warning}")

            # 执行删除操作
            self.logger.info(f"正在删除消费者组: {group_id}")

            # 使用Kafka Admin API删除消费者组
            delete_result = self.admin_client.delete_consumer_groups([group_id])

            # 检查删除结果
            if group_id in delete_result:
                future = delete_result[group_id]
                try:
                    future.result(timeout=30)  # 等待删除完成
                    self.logger.info(f"消费者组 {group_id} 删除成功")
                    return True
                except Exception as e:
                    self.logger.error(f"删除消费者组 {group_id} 失败: {e}")
                    return False
            else:
                self.logger.error(f"删除操作未返回消费者组 {group_id} 的结果")
                return False

        except Exception as e:
            self.logger.error(f"删除消费者组 {group_id} 时发生错误: {e}")
            return False

    def verify_group_deletion(self, group_id: str, max_wait_time: int = 60) -> bool:
        """
        验证消费者组是否已被完全删除

        参数:
            group_id: 消费者组ID
            max_wait_time: 最大等待时间（秒）

        返回:
            是否确认删除成功
        """
        start_time = time.time()
        check_interval = 2

        self.logger.info(f"验证消费者组 {group_id} 是否已被删除...")

        while time.time() - start_time < max_wait_time:
            try:
                # 使用轻量级检查方法
                exists, check_info = self.consumer_group_exists(group_id, retry_count=2, retry_delay=0.5)

                self.logger.debug(f"删除验证检查: exists={exists}, info={check_info}")

                if not exists:
                    self.logger.info(f"确认消费者组 {group_id} 已被完全删除")
                    return True

                elapsed = time.time() - start_time
                self.logger.info(f"消费者组 {group_id} 仍然存在，继续等待... ({elapsed:.1f}s/{max_wait_time}s)")
                time.sleep(check_interval)

            except Exception as e:
                self.logger.warning(f"验证删除状态时出错: {e}")
                time.sleep(check_interval)

        # 最后一次详细检查
        try:
            self.logger.info("执行最后一次详细检查...")
            group_info = self.get_consumer_group_info(group_id, retry_count=1, retry_delay=0.5)

            if not group_info.get('exists', False):
                self.logger.info(f"最终确认：消费者组 {group_id} 已被删除")
                return True
            else:
                state = group_info.get('state', 'Unknown')
                self.logger.warning(f"最终检查：消费者组 {group_id} 仍然存在，状态: {state}")
                return False

        except Exception as e:
            self.logger.error(f"最终验证时出错: {e}")
            return False

    def list_all_consumer_groups(self) -> List[Dict[str, Any]]:
        """
        列出所有消费者组

        返回:
            消费者组信息列表
        """
        try:
            groups_metadata = self.admin_client.list_consumer_groups()

            groups_info = []
            for group in groups_metadata:
                group_detail = self.get_consumer_group_info(group.group_id)
                groups_info.append(group_detail)

            return groups_info

        except Exception as e:
            self.logger.error(f"列出消费者组失败: {e}")
            return []

    def close(self) -> None:
        """关闭管理客户端"""
        if self.admin_client:
            try:
                self.admin_client.close()
                self.logger.info("Kafka管理客户端已关闭")
            except Exception as e:
                self.logger.error(f"关闭Kafka管理客户端失败: {e}")
            finally:
                self.admin_client = None
